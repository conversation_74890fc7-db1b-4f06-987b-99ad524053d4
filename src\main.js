import Vue from 'vue'
import App from './App.vue'
import router from './router'
import 'normalize.css'
import autofit from 'autofit.js'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

Vue.config.productionTip = false

// 使用Element UI
Vue.use(ElementUI)

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')

// 初始化autofit.js进行屏幕适配
autofit.init({
  dh: 1080, // 设计稿高度
  dw: 1920, // 设计稿宽度
  el: '#app', // 渲染的dom
  resize: true // 是否监听resize事件
})
